import { 
  Controller, 
  Get,
  Patch, 
  Post, 
  Delete, 
  Param, 
  Body,
  UseInterceptors, 
  UploadedFiles,
  BadRequestException,
  NotFoundException,
  UseGuards,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { FileUploadService } from './file-upload.service';
import { FileUpload } from './file-upload.entity';
import { TokenUsageResponseDto } from './dto/token-usage-response.dto';
import { Embedding } from './embedding.entity';
import { TestCaseGeneration } from './test-case-generation.entity';
import { UploadFileDto } from './dto/upload-file.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { AuthGuard } from '@nestjs/passport';

const ALLOWED_MIME_TYPES = [
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'text/markdown', // .md
  'text/plain', // .txt
  'application/pdf', // .pdf
  'text/html', // .html
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'text/csv' // .csv
];

@ApiTags('file-upload')
@ApiBearerAuth()
@Controller('file-upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Upload multiple files',
    description: 'Upload multiple files to Google Cloud Storage and create embeddings. Supports various document formats.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UploadFileDto })
  @ApiResponse({ 
    status: 201, 
    description: 'Files uploaded successfully', 
    type: [FileUpload] 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid file type or no files provided' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing JWT token' 
  })
  @UseInterceptors(AnyFilesInterceptor())
  async uploadFiles(@UploadedFiles() files: Express.Multer.File[]): Promise<FileUpload[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    // Validate all files before processing
    for (const file of files) {
      if (!ALLOWED_MIME_TYPES.includes(file.mimetype)) {
        throw new BadRequestException(`Invalid file type for file: ${file.originalname}`);
      }
    }

    // Process all files
    const uploadPromises = files.map(file => this.fileUploadService.uploadFile(file));
    return Promise.all(uploadPromises);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Get all uploaded files',
    description: 'Retrieve a list of all uploaded files for the authenticated user.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'List of uploaded files', 
    type: [FileUpload] 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing JWT token' 
  })
  async findAll(): Promise<FileUpload[]> {
    return this.fileUploadService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Get a specific file by ID',
    description: 'Retrieve details of a specific uploaded file by its ID.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'File found', 
    type: FileUpload 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing JWT token' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'File not found' 
  })
  async findOne(@Param('id') id: string): Promise<FileUpload> {
    const file = await this.fileUploadService.findOne(id);
    if (!file) {
      throw new NotFoundException('File not found');
    }
    return file;
  }

  @Get(':id/embeddings')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ 
    summary: 'Get embeddings for a file',
    description: 'Retrieve all embeddings generated for a specific file.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'List of embeddings', 
    type: [Embedding] 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing JWT token' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'File not found' 
  })
  async getEmbeddings(@Param('id') id: string): Promise<Embedding[]> {
    // Verify file exists
    await this.fileUploadService.findOne(id);
    return this.fileUploadService.getEmbeddings(id);
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete a file',
    description: 'Delete a file from both Google Cloud Storage and the database.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'File deleted successfully' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing JWT token' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'File not found' 
  })
  async remove(@Param('id') id: string): Promise<void> {
    return this.fileUploadService.remove(id);
  }

  @Get(':id/progress')
  @ApiOperation({ 
    summary: 'Get embedding generation progress',
    description: 'Get the progress of embedding generation for a specific file.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Progress percentage', 
    schema: {
      type: 'object',
      properties: {
        progress: {
          type: 'number',
          description: 'Progress percentage (0-100)',
          example: 45.5
        }
      }
    }
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing JWT token' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'File not found' 
  })
  async getProgress(@Param('id') id: string): Promise<{ progress: number }> {
    // Verify file exists
    await this.fileUploadService.findOne(id);
    const progress = await this.fileUploadService.getEmbeddingProgress(id);
    return { progress };
  }

  @Get(':id/embedding-usage')
  @ApiOperation({ 
    summary: 'Get embedding token usage',
    description: 'Get embedding token usage statistics for a specific file upload'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Token usage statistics',
    type: TokenUsageResponseDto
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getEmbeddingUsage(@Param('id') id: string) {
    return this.fileUploadService.getEmbeddingUsage(id);
  }

  @Get(':id/completion-usage')
  @ApiOperation({ 
    summary: 'Get completion token usage',
    description: 'Get completion token usage statistics for a specific file upload'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Token usage statistics',
    type: TokenUsageResponseDto
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getCompletionUsage(@Param('id') id: string) {
    return this.fileUploadService.getCompletionUsage(id);
  }

  @Post(':id/ai-generate-test-cases')
  @ApiOperation({ 
    summary: 'Generate test cases using AI',
    description: 'Generate test cases from file content using OpenAI'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Generated test cases',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          precondition: { type: 'string' },
          steps: { type: 'array', items: { type: 'string' } },
          expectation: { type: 'string' },
          testType: { type: 'string' },
          priority: { type: 'string' },
          platform: { type: 'string' },
          testCaseType: { type: 'string' },
          automationByAgentq: { type: 'boolean' }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async generateTestCases(@Param('id') id: string) {
    return this.fileUploadService.generateTestCases(id);
  }

  @Post(':id/ai-generate-more-test-cases')
  @ApiOperation({ 
    summary: 'Generate additional test cases using AI',
    description: 'Generate more test cases from file content using OpenAI, avoiding duplicates'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Generated additional test cases',
    type: [TestCaseGeneration]
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async generateMoreTestCases(@Param('id') id: string) {
    return this.fileUploadService.generateMoreTestCases(id);
  }

  @Get(':id/ai-generate-test-cases')
  @ApiOperation({ 
    summary: 'Get generated test cases',
    description: 'Get the generated test cases for a specific file'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Generated test cases retrieved successfully',
    type: [TestCaseGeneration]
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getGeneratedTestCases(@Param('id') id: string) {
    return this.fileUploadService.getGeneratedTestCases(id);
  }

  @Patch(':fileUploadId/ai-generate-test-cases/:id')
  @ApiOperation({ 
    summary: 'Update a generated test case',
    description: 'Update a specific generated test case by ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Test case updated successfully',
    type: TestCaseGeneration
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async updateGeneratedTestCase(
    @Param('fileUploadId') fileUploadId: string,
    @Param('id') id: string,
    @Body() updateData: Partial<TestCaseGeneration>
  ) {
    return this.fileUploadService.updateGeneratedTestCase(fileUploadId, id, updateData);
  }

  @Delete(':fileUploadId/ai-generate-test-cases/:id')
  @ApiOperation({ 
    summary: 'Delete a generated test case',
    description: 'Delete a specific generated test case by ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Test case deleted successfully'
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async deleteGeneratedTestCase(
    @Param('fileUploadId') fileUploadId: string,
    @Param('id') id: string
  ) {
    return this.fileUploadService.deleteGeneratedTestCase(fileUploadId, id);
  }
}