// import { Test, TestingModule } from '@nestjs/testing';
// import { ConfigService } from '@nestjs/config';
// import { getRepositoryToken } from '@nestjs/typeorm';
// import { Repository } from 'typeorm';
// import { ContentAnalysisService } from './content-analysis.service';
// import { TokenUsage } from './token-usage.entity';

// describe('ContentAnalysisService', () => {
//   let service: ContentAnalysisService;
//   let configService: ConfigService;
//   let tokenUsageRepository: Repository<TokenUsage>;

//   const mockConfigService = {
//     get: jest.fn((key: string) => {
//       switch (key) {
//         case 'GEMINI_API_KEY':
//           return 'test-api-key';
//         default:
//           return undefined;
//       }
//     }),
//   };

//   const mockTokenUsageRepository = {
//     save: jest.fn(),
//   };

//   beforeEach(async () => {
//     const module: TestingModule = await Test.createTestingModule({
//       providers: [
//         ContentAnalysisService,
//         {
//           provide: ConfigService,
//           useValue: mockConfigService,
//         },
//         {
//           provide: getRepositoryToken(TokenUsage),
//           useValue: mockTokenUsageRepository,
//         },
//       ],
//     }).compile();

//     service = module.get<ContentAnalysisService>(ContentAnalysisService);
//     configService = module.get<ConfigService>(ConfigService);
//     tokenUsageRepository = module.get<Repository<TokenUsage>>(getRepositoryToken(TokenUsage));
//   });

//   it('should be defined', () => {
//     expect(service).toBeDefined();
//   });

//   describe('supportsMultimodalAnalysis', () => {
//     it('should return true for PDF files', () => {
//       const mockFile = {
//         mimetype: 'application/pdf',
//         originalname: 'test.pdf',
//         buffer: Buffer.from('test'),
//       } as Express.Multer.File;

//       // Access private method for testing
//       const result = (service as any).supportsMultimodalAnalysis(mockFile.mimetype);
//       expect(result).toBe(true);
//     });

//     it('should return true for image files', () => {
//       const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      
//       imageTypes.forEach(mimetype => {
//         const result = (service as any).supportsMultimodalAnalysis(mimetype);
//         expect(result).toBe(true);
//       });
//     });

//     it('should return false for text files', () => {
//       const textTypes = ['text/plain', 'text/markdown', 'text/html'];
      
//       textTypes.forEach(mimetype => {
//         const result = (service as any).supportsMultimodalAnalysis(mimetype);
//         expect(result).toBe(false);
//       });
//     });
//   });

//   describe('createMultimodalAnalysisPrompt', () => {
//     it('should create a comprehensive prompt for multimodal analysis', () => {
//       const filename = 'test-document.pdf';
//       const mimetype = 'application/pdf';
      
//       const prompt = (service as any).createMultimodalAnalysisPrompt(filename, mimetype);
      
//       expect(prompt).toContain(filename);
//       expect(prompt).toContain(mimetype);
//       expect(prompt).toContain('Visual Elements');
//       expect(prompt).toContain('test case generation');
//       expect(prompt).toContain('user interface elements');
//     });
//   });

//   describe('createTextOnlyAnalysisPrompt', () => {
//     it('should create a prompt for text-only analysis', () => {
//       const filename = 'test-document.txt';
//       const textContent = 'This is a test document with some content.';
      
//       const prompt = (service as any).createTextOnlyAnalysisPrompt(filename, textContent);
      
//       expect(prompt).toContain(filename);
//       expect(prompt).toContain(textContent);
//       expect(prompt).toContain('Main Topics');
//       expect(prompt).toContain('test case generation');
//     });

//     it('should truncate long text content', () => {
//       const filename = 'long-document.txt';
//       const longContent = 'a'.repeat(10000); // 10k characters
      
//       const prompt = (service as any).createTextOnlyAnalysisPrompt(filename, longContent);
      
//       expect(prompt).toContain('[truncated]');
//       expect(prompt.length).toBeLessThan(longContent.length + 1000); // Should be significantly shorter
//     });
//   });

//   describe('analyzePdfContent', () => {
//     it('should detect images in PDF based on heuristics', async () => {
//       // Mock a PDF buffer that's much larger than its text content would suggest
//       const largePdfBuffer = Buffer.alloc(100000); // 100KB buffer
      
//       // Mock pdf-parse to return minimal text
//       jest.doMock('pdf-parse', () => {
//         return jest.fn().mockResolvedValue({
//           text: 'Small amount of text' // Much smaller than buffer size
//         });
//       });

//       const result = await (service as any).analyzePdfContent(largePdfBuffer);
      
//       expect(result).toHaveProperty('hasImages');
//       expect(result).toHaveProperty('imageCount');
//       expect(typeof result.hasImages).toBe('boolean');
//       expect(typeof result.imageCount).toBe('number');
//     });
//   });

//   // Note: Integration tests with actual Gemini API would require API keys
//   // and should be run separately from unit tests
//   describe('Integration Tests (Skipped)', () => {
//     it.skip('should analyze actual image file with Gemini', async () => {
//       // This test would require actual Gemini API key and should be run separately
//       // const mockImageFile = createMockImageFile();
//       // const result = await service.analyzeFileContent(mockImageFile, 'test-embedding-id');
//       // expect(result.description).toBeDefined();
//       // expect(result.metadata.analysisType).toBe('multimodal');
//     });

//     it.skip('should analyze actual PDF file with Gemini', async () => {
//       // This test would require actual Gemini API key and should be run separately
//       // const mockPdfFile = createMockPdfFile();
//       // const result = await service.analyzeFileContent(mockPdfFile, 'test-embedding-id');
//       // expect(result.description).toBeDefined();
//     });
//   });
// });

// // Helper functions for creating mock files (for future integration tests)
// function createMockImageFile(): Express.Multer.File {
//   return {
//     fieldname: 'file',
//     originalname: 'test-image.jpg',
//     encoding: '7bit',
//     mimetype: 'image/jpeg',
//     size: 1024,
//     buffer: Buffer.from('fake-image-data'),
//     destination: '',
//     filename: '',
//     path: '',
//     stream: null as any,
//   };
// }

// function createMockPdfFile(): Express.Multer.File {
//   return {
//     fieldname: 'file',
//     originalname: 'test-document.pdf',
//     encoding: '7bit',
//     mimetype: 'application/pdf',
//     size: 2048,
//     buffer: Buffer.from('fake-pdf-data'),
//     destination: '',
//     filename: '',
//     path: '',
//     stream: null as any,
//   };
// }
