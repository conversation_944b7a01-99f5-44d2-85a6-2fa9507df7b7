import { Injectable } from '@nestjs/common';
import * as pdfjsLib from 'pdf-parse';
import * as mammoth from 'mammoth';

@Injectable()
export class FileProcessorService {
  async extractText(file: Express.Multer.File): Promise<string> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.extractFromPDF(file.buffer);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.extractFromDOCX(file.buffer);
      case 'text/plain':
      case 'text/markdown':
      case 'text/html':
      case 'text/csv':
        return this.extractFromText(file.buffer);
      default:
        throw new Error(`Unsupported file type: ${file.mimetype}`);
    }
  }

  private async extractFromPDF(buffer: Buffer): Promise<string> {
    const data = await pdfjsLib(buffer);
    return data.text;
  }

  private async extractFromDOCX(buffer: Buffer): Promise<string> {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  private async extractFromText(buffer: Buffer): Promise<string> {
    return buffer.toString('utf-8');
  }

  splitIntoChunks(text: string, maxChunkSize: number = 1000): string[] {
    const sentences = text.match(/[^.!?]+[.!?]+/g) || [];
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length <= maxChunkSize) {
        currentChunk += sentence;
      } else {
        if (currentChunk) chunks.push(currentChunk.trim());
        currentChunk = sentence;
      }
    }

    if (currentChunk) chunks.push(currentChunk.trim());
    return chunks;
  }
}